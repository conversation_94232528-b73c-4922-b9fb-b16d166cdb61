# Claude Code 包括的エージェントコレクション

**Language**: [English](README.md) | [日本語](README_JA.md)

要件分析から本番デプロイ・継続的メンテナンスまで、ソフトウェア開発ライフサイクル全体の自動化を可能にする専門サブエージェントの完全コレクションです。

## 🎯 概要

このリポジトリには、人間の介入を最小限に抑えて完全なソフトウェア開発ライフサイクルを処理できるように設計されたClaude Code用の包括的なサブエージェントセットが含まれています。エージェントは現代のソフトウェア開発のあらゆる側面をカバーする6つのカテゴリに整理されています。

## 📦 エージェントカテゴリ

### 1. 要件定義・分析
**目的**: ビジネスニーズを詳細な技術仕様に変換

- **requirements-analyst** - ユーザーニーズを分析し、詳細な機能仕様を作成
- **user-story-generator** - 包括的なユーザーストーリーと受け入れ基準を作成
- **business-process-analyst** - ビジネスプロセスを分析し、技術要件に変換
- **requirements-validator** - 要件の完全性と一貫性を検証

### 2. 設計・アーキテクチャ
**目的**: 堅牢でスケーラブルなシステム設計を作成

- **system-architect** - 包括的なシステムアーキテクチャと技術スタックを設計
- **data-architect** - データモデル、スキーマ、統合戦略を設計
- **interface-designer** - ユーザーインターフェースとAPI仕様を設計
- **security-architect** - セキュリティフレームワークとデータ保護戦略を設計
- **design-reviewer** - システム設計の品質をレビュー・検証

### 3. 実装・開発
**目的**: コード開発と品質保証のあらゆる側面を処理

- **code-reviewer** - 包括的なコード品質評価を実行
- **test-suite-generator** - 包括的なテストカバレッジを生成
- **code-refactoring-specialist** - コード構造を安全に改善し、技術的負債を削減
- **security-analyzer** - 脆弱性とセキュリティ問題を特定
- **performance-optimizer** - コードパフォーマンスを分析・最適化
- **api-designer** - 適切な仕様を持つクリーンなRESTful APIを設計
- **documentation-generator** - 技術文書とコードコメントを作成
- **dependency-manager** - パッケージ依存関係を管理し、競合を解決
- **database-schema-designer** - 効率的なデータベーススキーマとマイグレーションを設計
- **git-manager** - Git操作、コミット整理、リポジトリメンテナンスを管理
- **cicd-builder** - CI/CDパイプラインを作成・設定

### 4. プロジェクト管理
**目的**: 開発プロセス全体を調整・管理

- **project-planner** - 包括的なプロジェクト計画とタイムラインを作成
- **risk-manager** - プロジェクトリスクを特定し、緩和戦略を作成
- **progress-tracker** - プロジェクトの進捗を監視し、ブロッカーを特定
- **qa-coordinator** - 品質基準を確立し、テストを調整
- **stakeholder-communicator** - ステークホルダーコミュニケーションとレポートを管理

### 5. デプロイ・運用
**目的**: 本番デプロイと継続的運用を処理

- **project-orchestrator** - エンドツーエンドプロジェクト実行のマスターコーディネーター
- **deployment-ops-manager** - 本番デプロイと運用監視を処理
- **uat-coordinator** - ビジネスステークホルダーとのユーザー受け入れテストを調整
- **training-change-manager** - トレーニング資料を作成し、システム導入を管理
- **project-template-manager** - プロジェクトテンプレートの管理と一般的なプロジェクトパターンの迅速セットアップ

### 6. メタ管理
**目的**: Claude Code自体を最大効率で最適化

- **context-manager** - セッションコンテキストを監視し、継続性のための情報を管理
- **session-continuity-manager** - Claude Codeセッション間のシームレスな移行を保証
- **memory-manager** - Claude Codeのメモリ使用量とプロジェクト文書を最適化
- **workflow-optimizer** - 開発ワークフローとエージェント使用を分析・最適化
- **resource-monitor** - リソース使用量を監視し、最適化戦略を提案
- **agent-creator** - プロジェクトのニーズに応じて新しい専門エージェントを動的に作成

## 🚀 主要機能

### 完全自動化
- **エンドツーエンド開発**: 要件から本番デプロイまで
- **インテリジェントオーケストレーション**: エージェントが自動的に作業を調整・シーケンス
- **動的専門化**: ユニークなプロジェクトニーズに対応する新しいエージェントを作成
- **セッション継続性**: 長期開発セッション全体でコンテキストを維持

### プロフェッショナル品質
- **業界ベストプラクティス**: 各エージェントが確立された方法論に従う
- **包括的テスト**: 自動テスト生成と品質保証
- **セキュリティファースト**: 組み込みセキュリティ分析とコンプライアンスチェック
- **本番対応**: 完全なデプロイと運用サポート

### スケーラブルアーキテクチャ
- **モジュラー設計**: 個別エージェントまたは完全ワークフローを使用
- **コンテキスト保持**: 長期プロジェクトの効率的メモリ管理
- **リソース最適化**: Claude Code使用量の監視と最適化
- **テンプレート駆動**: 実証済みパターンによる迅速なプロジェクトセットアップ

## 💡 使用例

### 完全プロジェクト自動化
```
ユーザー: 「会社用の図書管理システムを作って」
結果: データベース、API、フロントエンド、テスト、文書、デプロイを含む完全に機能するWebアプリケーション
```

### 専門開発タスク
```
ユーザー: 「この認証コードをセキュリティ問題について確認して」
エージェント: security-analyzerが包括的なセキュリティ監査を実行
```

### 長期プロジェクト管理
```
ユーザー: 「マルチテナントSaaSプラットフォームの開発を管理して」
エージェント: project-orchestratorが適切な専門家とすべてのフェーズを調整
```

## 📋 インストール

1. **エージェント定義をコピー**してプロジェクトの`.claude/agents/`ディレクトリに配置:
   ```bash
   mkdir -p .claude/agents
   # エージェント定義ファイルをこのディレクトリにコピー
   ```

2. **インストールを確認**:
   ```bash
   ls .claude/agents/
   # エージェントファイル（.md形式）が表示されるはず
   ```

3. **Claude Codeでエージェントを使用開始**:
   ```
   project-orchestratorエージェントを使って完全なWebアプリケーションを構築
   ```

## 🎮 使用例

### 新しいWebアプリケーションの開始
```
「ユーザー認証、リアルタイム更新、モバイル対応を持つタスク管理Webアプリケーションを作りたい。要件からデプロイまですべて処理して。」
```

**project-orchestrator**が以下を実行:
1. **requirements-analyst**を使用して詳細要件を収集
2. 設計のために**system-architect**と**data-architect**を調整
3. 開発エージェントで実装を管理
4. テスト、デプロイ、文書化を処理
5. エンドユーザー向けトレーニング資料を提供

### コード品質レビュー
```
「eコマースのチェックアウトプロセスをセキュリティ脆弱性、パフォーマンス問題、コード品質について確認して」
```

複数エージェントが連携:
- **security-analyzer**が脆弱性をチェック
- **performance-optimizer**がボトルネックを特定
- **code-reviewer**がベストプラクティスを確認

### 長期プロジェクト管理
```
「今後6ヶ月間、定期的なステークホルダー更新を含む新しい顧客ポータルの開発を管理して」
```

システムが提供:
- 自動プロジェクト計画とリスク管理
- 定期的な進捗追跡とレポート
- 品質ゲートとテスト調整
- ステークホルダーコミュニケーション管理

## 🔧 エージェントワークフローパターン

### シーケンシャルパターン
要件 → 設計 → 実装 → テスト → デプロイ

### パラレルパターン
異なるコンポーネントで同時に作業する複数の開発エージェント

### アダプティブパターン
**agent-creator**がユニークな要件に対応する専門エージェントを生成

### 継続パターン
メタ管理エージェントが継続的な最適化と監視を提供

## 📝 エージェント定義フォーマット

各エージェントはClaude Codeの標準フォーマットに従います:
```markdown
---
name: エージェント名
description: 例と使用パターンを含む詳細説明
---

エージェントの専門知識、責任、方法論を定義する包括的システムプロンプト
```

## 🔄 エージェント相互作用

### マスターコーディネーター
- **project-orchestrator**が全体的なプロジェクトフローを管理
- 適切なエージェントを自動選択・シーケンス
- エージェント間コミュニケーションと依存関係管理を処理

### 専門チーム
- **要件チーム**: プロジェクトニーズの収集と検証
- **設計チーム**: 技術アーキテクチャと仕様の作成
- **開発チーム**: コードの実装、テスト、最適化
- **運用チーム**: 本番システムのデプロイと保守
- **メタチーム**: Claude Code使用と継続性の最適化

## 🎯 完全自動化の例

### 入力
```
「会社用の図書管理システムを作って」
```

### 自動プロセス
1. **要件分析**: ステークホルダーニーズ → 技術仕様
2. **システム設計**: アーキテクチャ → データベース設計 → API設計 → UI設計
3. **実装**: バックエンド → フロントエンド → テスト → 文書化
4. **品質保証**: コードレビュー → セキュリティ分析 → パフォーマンス最適化
5. **デプロイ**: 本番セットアップ → CI/CDパイプライン → 監視
6. **引き渡し**: ユーザートレーニング → 文書化 → サポート手順

### 出力
- 完全に機能するWebアプリケーション
- 高カバレッジの完全なテストスイート
- 監視付き本番デプロイ
- ユーザー文書とトレーニング資料
- 継続的メンテナンス手順

## 🤝 貢献

貢献を歓迎します！以下をお願いします:

1. 確立されたエージェント定義フォーマットに従う
2. 包括的な例と文書を含める
3. 実際のプロジェクトで徹底的にテスト
4. エージェントが既存ワークフローとよく統合されることを確認
5. エージェント機能の明確な文書を提出

## 📄 ライセンス

MITライセンス - 自由に使用、修正、配布してください。

## 🙏 謝辞

[Claude Code](https://claude.ai/code)とシームレスに動作するよう設計され、サブエージェント開発のすべての確立されたパターンとベストプラクティスに従っています。

## 📞 サポート

問題、質問、提案については:
- このリポジトリでissueを開く
- https://docs.anthropic.com/en/docs/claude-code でClaude Code文書を確認
- エージェントの例と使用パターンを確認

---

*インテリジェント自動化で開発プロセスを変革しましょう。単一の要件から本番システムまで - エージェントが複雑さを処理し、あなたはビジョンに集中できます。*

## 🚀 クイックスタート

1. **エージェントをコピー**してプロジェクトの`.claude/agents/`ディレクトリに配置
2. **Claude Codeを開始**してプロジェクトで実行
3. **指示**: 「project-orchestratorを使って[プロジェクト説明]を構築して」
4. **確認**: システムが要件からデプロイまですべてを処理

**これだけです！** エージェントが自動的に連携して、完全な本番対応ソリューションを提供します。